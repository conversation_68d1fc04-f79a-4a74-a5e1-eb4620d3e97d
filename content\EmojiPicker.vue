<template>
  <div class="fk-d-menu -animated -expanded emoji-picker-container" style="max-width: 400px; visibility: visible">
    <div class="fk-d-menu__inner-content">
      <div class="emoji-picker">
        <div class="emoji-picker__filter-container">
          <div class="emoji-picker__filter filter-input-container">
            <input
              v-model="searchQuery"
              class="filter-input"
              placeholder="搜索表情符号..."
              type="text"
            />
            <svg
              class="fa d-icon d-icon-magnifying-glass svg-icon -right svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#magnifying-glass"></use>
            </svg>
          </div>
          <button 
            @click="$emit('close')"
            class="btn no-text btn-icon btn-transparent"
            title="关闭"
          >
            <svg class="fa d-icon d-icon-times svg-icon svg-string" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="emoji-picker__content">
          <div class="emoji-picker__sections-nav">
            <button
              v-for="group in sortedGroups"
              :key="group.id"
              @click="activeGroupId = group.id"
              :class="['btn no-text btn-flat emoji-picker__section-btn', { active: activeGroupId === group.id }]"
              :title="group.name"
            >
              <span class="emoji-picker-section-icon">{{ group.icon }}</span>
            </button>
          </div>
          <div class="emoji-picker__scrollable-content">
            <div v-if="isLoading" class="loading-spinner"></div>
            <div v-else class="emoji-picker__sections" role="button">
              <div
                class="emoji-picker__section"
                v-if="filteredEmojis.length > 0"
                role="region"
              >
                <div class="emoji-picker__section-title-container">
                  <h2 class="emoji-picker__section-title">{{ activeGroup?.name }}</h2>
                </div>
                <div class="emoji-picker__section-emojis">
                  <img
                    v-for="emoji in filteredEmojis"
                    :key="emoji.id"
                    @click="selectEmoji(emoji)"
                    :src="emoji.url"
                    :alt="emoji.name"
                    :title="emoji.name"
                    class="emoji"
                    loading="lazy"
                  />
                </div>
              </div>
              <div v-else class="empty-state">
                <p>{{ searchQuery ? '没有找到匹配的表情' : '该分组还没有表情' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useEmojiStore } from '../src/stores/emojiStore';
import type { Emoji } from '../src/types/emoji';

defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  select: [emoji: Emoji];
  close: [];
}>();

const emojiStore = useEmojiStore();

const searchQuery = ref('');
const activeGroupId = ref('nachoneko');
const isLoading = ref(true);

const activeGroup = computed(() => 
  emojiStore.groups.find(g => g.id === activeGroupId.value) || (emojiStore.groups.length > 0 ? emojiStore.groups[0] : null)
);

const filteredEmojis = computed(() => {
  if (!activeGroup.value) return [];
  
  let emojis = activeGroup.value.emojis;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    // Search across all groups if there is a search query
    if (searchQuery.value) {
        emojis = emojiStore.getAllEmojis().filter(emoji => 
            emoji.name.toLowerCase().includes(query)
        );
    } else {
        emojis = activeGroup.value.emojis;
    }
  }
  
  return emojis;
});

const sortedGroups = computed(() => 
  [...emojiStore.groups].sort((a, b) => a.order - b.order)
);

const selectEmoji = (emoji: Emoji) => {
  emit('select', emoji);
  emit('close');
};

onMounted(async () => {
  isLoading.value = true;
  await emojiStore.loadData();
  if (emojiStore.groups.length > 0) {
    activeGroupId.value = emojiStore.settings.defaultGroup || emojiStore.groups[0].id;
  }
  isLoading.value = false;
});

</script>

<style>
:root {
  --d-menu-background-color: #fff;
  --d-menu-border-radius: 8px;
  --d-menu-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --d-menu-padding: 8px;
  --primary-low: #e9e9e9;
  --primary-medium: #777;
  --primary: #222;
  --tertiary: #007bff;
}

.emoji-picker-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  width: 350px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
  background-color: var(--d-menu-background-color);
  border-radius: var(--d-menu-border-radius);
  box-shadow: var(--d-menu-box-shadow);
}

.fk-d-menu__inner-content {
  padding: var(--d-menu-padding);
}

.emoji-picker {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.emoji-picker__filter-container {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--primary-low);
}

.filter-input-container {
  flex-grow: 1;
  position: relative;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.filter-input:focus {
  outline: none;
  border-color: var(--tertiary);
  box-shadow: 0 0 0 1px var(--tertiary);
}

.emoji-picker__content {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  margin-top: 8px;
}

.emoji-picker__sections-nav {
  display: flex;
  flex-direction: column;
  padding-right: 8px;
  border-right: 1px solid var(--primary-low);
}

.emoji-picker__section-btn {
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  background-color: transparent;
  border: none;
}

.emoji-picker__section-btn.active {
  background-color: #e0e0e0;
}

.emoji-picker-section-icon {
  font-size: 20px;
}

.emoji-picker__scrollable-content {
  flex-grow: 1;
  overflow-y: auto;
  padding-left: 8px;
}

.emoji-picker__section-title-container {
  padding-bottom: 8px;
}

.emoji-picker__section-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-medium);
}

.emoji-picker__section-emojis {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 4px;
}

.emoji {
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: 4px;
  transition: transform 0.1s;
}

.emoji:hover {
  transform: scale(1.1);
  background-color: #f0f0f0;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: var(--primary-medium);
}
</style>